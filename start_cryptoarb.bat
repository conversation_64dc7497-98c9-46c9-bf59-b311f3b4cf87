@echo off
echo ========================================
echo    CryptoArb Pro - Startup Script
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python tidak ditemukan. Pastikan Python sudah terinstall.
    echo [INFO] Download Python dari: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [INFO] Python ditemukan, melanjutkan...

REM Check if required files exist
if not exist "main.py" (
    echo [ERROR] File main.py tidak ditemukan!
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo [ERROR] File requirements.txt tidak ditemukan!
    pause
    exit /b 1
)

echo [INFO] Memeriksa dependencies...

REM Install dependencies if needed
pip install -r requirements.txt --quiet

if errorlevel 1 (
    echo [ERROR] Gagal menginstall dependencies.
    echo [INFO] Coba jalankan: pip install flask requests
    pause
    exit /b 1
)

echo [INFO] Dependencies OK, memulai CryptoArb Pro...
echo.
echo ========================================
echo  Pilih mode operasi:
echo  1. Mode Normal (API Real)
echo  2. Mode Demo (Data Simulasi)
echo ========================================
echo.

set /p choice="Masukkan pilihan (1 atau 2): "

if "%choice%"=="1" (
    echo [INFO] Memulai mode normal...
    python main.py
) else if "%choice%"=="2" (
    echo [INFO] Memulai mode demo...
    python run_demo.py
) else (
    echo [ERROR] Pilihan tidak valid. Menggunakan mode normal...
    python main.py
)

echo.
echo [INFO] CryptoArb Pro telah berhenti.
pause
