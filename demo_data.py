# -*- coding: utf-8 -*-
"""
Demo data untuk testing CryptoArb Pro ketika API tidak tersedia
"""

import random
import time
from datetime import datetime

def generate_demo_binance_data():
    """Generate demo data untuk Binance API"""
    symbols = [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'ADAUSDT',
        'DOGEUSDT', 'XRPUSDT', 'MATICUSDT', 'AVAXUSDT', 'DOTUSDT',
        'LINKUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT', 'LTCUSDT',
        'TRXUSDT', 'NEARUSDT', 'APTUSDT', 'OPUSDT', 'ARBUSDT',
        'SHIBUSDT', 'PEPEUSDT', 'FLOKIUSDT', 'BONKUSDT', 'WIFUSDT'
    ]
    
    base_prices = {
        'BTCUSDT': 43000, 'ETHUSDT': 2600, 'BNBUSDT': 310, 'SOLUSDT': 95,
        'ADAUSDT': 0.45, 'DOGEUSDT': 0.08, 'XRPUSDT': 0.52, 'MATICUSDT': 0.85,
        'AVAXUSDT': 38, 'DOTUSDT': 7.2, 'LINKUSDT': 15, 'UNIUSDT': 6.8,
        'ATOMUSDT': 9.5, 'FILUSDT': 5.2, 'LTCUSDT': 72, 'TRXUSDT': 0.11,
        'NEARUSDT': 2.1, 'APTUSDT': 8.5, 'OPUSDT': 2.3, 'ARBUSDT': 1.2,
        'SHIBUSDT': 0.000024, 'PEPEUSDT': 0.00000085, 'FLOKIUSDT': 0.00015,
        'BONKUSDT': 0.000032, 'WIFUSDT': 2.8
    }
    
    data = []
    for symbol in symbols:
        base_price = base_prices.get(symbol, 1.0)
        # Add some random variation (-2% to +2%)
        price_variation = random.uniform(-0.02, 0.02)
        current_price = base_price * (1 + price_variation)
        
        volume = random.uniform(50000, 5000000)  # Random volume
        
        data.append({
            'symbol': symbol,
            'lastPrice': str(current_price),
            'quoteVolume': str(volume),
            'priceChange': str(random.uniform(-0.05, 0.05) * current_price),
            'priceChangePercent': str(random.uniform(-5, 5)),
            'volume': str(random.uniform(1000, 100000)),
            'count': random.randint(1000, 50000)
        })
    
    return data

def generate_demo_bybit_data():
    """Generate demo data untuk Bybit API"""
    symbols = [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'ADAUSDT',
        'DOGEUSDT', 'XRPUSDT', 'MATICUSDT', 'AVAXUSDT', 'DOTUSDT',
        'LINKUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT', 'LTCUSDT',
        'TRXUSDT', 'NEARUSDT', 'APTUSDT', 'OPUSDT', 'ARBUSDT',
        'SHIBUSDT', 'PEPEUSDT', 'FLOKIUSDT', 'BONKUSDT', 'WIFUSDT'
    ]
    
    base_prices = {
        'BTCUSDT': 43000, 'ETHUSDT': 2600, 'BNBUSDT': 310, 'SOLUSDT': 95,
        'ADAUSDT': 0.45, 'DOGEUSDT': 0.08, 'XRPUSDT': 0.52, 'MATICUSDT': 0.85,
        'AVAXUSDT': 38, 'DOTUSDT': 7.2, 'LINKUSDT': 15, 'UNIUSDT': 6.8,
        'ATOMUSDT': 9.5, 'FILUSDT': 5.2, 'LTCUSDT': 72, 'TRXUSDT': 0.11,
        'NEARUSDT': 2.1, 'APTUSDT': 8.5, 'OPUSDT': 2.3, 'ARBUSDT': 1.2,
        'SHIBUSDT': 0.000024, 'PEPEUSDT': 0.00000085, 'FLOKIUSDT': 0.00015,
        'BONKUSDT': 0.000032, 'WIFUSDT': 2.8
    }
    
    list_data = []
    for symbol in symbols:
        base_price = base_prices.get(symbol, 1.0)
        # Add different variation for arbitrage opportunities
        price_variation = random.uniform(-0.03, 0.03)  # Slightly different range
        current_price = base_price * (1 + price_variation)
        
        # Sometimes create intentional arbitrage opportunities
        if random.random() < 0.3:  # 30% chance
            if random.random() < 0.5:
                current_price *= 1.01  # 1% higher
            else:
                current_price *= 0.99  # 1% lower
        
        volume = random.uniform(40000, 4500000)  # Slightly different volume range
        
        list_data.append({
            'symbol': symbol,
            'lastPrice': str(current_price),
            'turnover24h': str(volume),
            'price24hPcnt': str(random.uniform(-0.05, 0.05)),
            'volume24h': str(random.uniform(1000, 100000)),
            'usdIndexPrice': str(current_price * random.uniform(0.999, 1.001))
        })
    
    return {
        'retCode': 0,
        'retMsg': 'OK',
        'result': {
            'category': 'spot',
            'list': list_data
        },
        'retExtInfo': {},
        'time': int(time.time() * 1000)
    }

def create_demo_opportunities():
    """Create some demo arbitrage opportunities"""
    opportunities = [
        {
            'symbol': 'BTCUSDT',
            'binance_price': 43150.50,
            'bybit_price': 43280.75,
            'profit_percentage': 0.302,
            'volume_24h': 2500000,
            'direction': 'binance_to_bybit',
            'networks': ['ETH', 'BSC', 'TRC20']
        },
        {
            'symbol': 'ETHUSDT', 
            'binance_price': 2615.80,
            'bybit_price': 2598.45,
            'profit_percentage': 0.664,
            'volume_24h': 1800000,
            'direction': 'bybit_to_binance',
            'networks': ['ETH', 'POLYGON', 'ARBITRUM']
        },
        {
            'symbol': 'SOLUSDT',
            'binance_price': 94.25,
            'bybit_price': 95.85,
            'profit_percentage': 1.698,
            'volume_24h': 850000,
            'direction': 'binance_to_bybit',
            'networks': ['ETH', 'BSC']
        },
        {
            'symbol': 'PEPEUSDT',
            'binance_price': 0.00000082,
            'bybit_price': 0.00000088,
            'profit_percentage': 7.317,
            'volume_24h': 450000,
            'direction': 'binance_to_bybit',
            'networks': ['ETH', 'BSC', 'POLYGON']
        },
        {
            'symbol': 'WIFUSDT',
            'binance_price': 2.85,
            'bybit_price': 2.72,
            'profit_percentage': 4.779,
            'volume_24h': 320000,
            'direction': 'bybit_to_binance',
            'networks': ['ETH', 'BSC']
        }
    ]
    
    return opportunities

# Demo mode flag
DEMO_MODE = True

def get_demo_status():
    """Get demo system status"""
    return {
        'binance_healthy': True,
        'bybit_healthy': True,
        'last_update': datetime.now().isoformat(),
        'opportunities_count': 5,
        'stats': {
            'total_scanned': 25,
            'opportunities_found': 5,
            'average_profit': 2.952,
            'scan_duration': 1.25
        }
    }
