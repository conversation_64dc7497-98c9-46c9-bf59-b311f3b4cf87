2025-07-13 02:40:37,251 - __main__ - INFO - Background scanner started
2025-07-13 02:40:38,780 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:40:38,788 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:40:40,320 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:40:40,321 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:40:40,321 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:40:40,322 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:40:40,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:40:40,372 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:40:41,673 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:40:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:40:43,062 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:40:43] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:06,098 - __main__ - INFO - Background scanner started
2025-07-13 02:41:07,626 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:41:07,627 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:41:09,155 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:41:09,155 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:41:09,158 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:41:09,159 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:41:09,207 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:41:09,208 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:41:10,673 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:41:10,973 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:41:10,974 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:41:21,474 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:21] "GET / HTTP/1.1" 200 -
2025-07-13 02:41:25,756 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:25] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:27,392 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:27] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:27,394 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:27] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:41:28,206 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:28] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-13 02:41:38,026 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:38] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:41,411 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:41:41,719 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:41:41,720 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:41:44,294 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:44] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:48,984 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:48] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:58,059 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:58] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:58,363 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:58] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:42:02,217 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:02] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:08,368 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:08] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:12,047 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:42:12,365 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:42:12,365 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:42:18,652 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:18] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:27,696 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:27] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:27,700 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:27] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:42:38,002 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:38] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:42,696 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:42:42,999 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:42:43,000 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:42:47,689 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:47] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:57,685 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:57] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:42:57,995 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:57] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:08,971 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:08] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:13,340 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:43:13,655 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:43:13,656 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:43:18,046 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:18] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:28,361 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:28] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:43:29,080 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:29] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:38,050 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:38] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:44,006 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:43:44,314 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:43:44,315 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:43:49,021 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:49] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:58,059 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:58] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:58,368 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:58] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:44:07,362 - __main__ - INFO - [OK] All dependencies available
2025-07-13 02:44:07,365 - __main__ - INFO - [INFO] Testing API connections...
2025-07-13 02:44:09,194 - __main__ - WARNING - [WARN] Binance API returned status 451
2025-07-13 02:44:11,035 - __main__ - WARNING - [WARN] Bybit API returned status 403
2025-07-13 02:44:11,036 - __main__ - INFO - [START] Starting CryptoArb Pro...
2025-07-13 02:44:11,038 - __main__ - INFO - Background scanner started
2025-07-13 02:44:11,039 - __main__ - INFO - [SCAN] Performing initial scan...
2025-07-13 02:44:12,547 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:12,565 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:13,786 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:13,787 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:13,828 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:13,828 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:13,829 - __main__ - INFO - [RESULT] Initial scan found 0 opportunities
2025-07-13 02:44:13,829 - __main__ - INFO - [WEB] Starting web interface...
2025-07-13 02:44:13,831 - __main__ - INFO - [INFO] Access the application at: http://localhost:5000
2025-07-13 02:44:13,832 - __main__ - INFO - [INFO] Use Ctrl+C to stop the application
2025-07-13 02:44:13,856 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:44:13,857 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:44:28,360 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:44:28] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:44:40,502 - __main__ - INFO - [OK] All dependencies available
2025-07-13 02:44:40,502 - __main__ - INFO - [INFO] Testing API connections...
2025-07-13 02:44:41,677 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:44:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:44:42,064 - __main__ - WARNING - [WARN] Binance API returned status 451
2025-07-13 02:44:43,576 - __main__ - WARNING - [WARN] Bybit API returned status 403
2025-07-13 02:44:43,577 - __main__ - INFO - [START] Starting CryptoArb Pro...
2025-07-13 02:44:43,578 - __main__ - INFO - Background scanner started
2025-07-13 02:44:43,579 - __main__ - INFO - [SCAN] Performing initial scan...
2025-07-13 02:44:44,107 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:44,423 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:44,423 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:45,096 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:45,096 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:46,621 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:46,621 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:46,653 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:46,655 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:46,655 - __main__ - INFO - [RESULT] Initial scan found 0 opportunities
2025-07-13 02:44:46,656 - __main__ - INFO - [WEB] Starting web interface...
2025-07-13 02:44:46,656 - __main__ - INFO - [INFO] Access the application at: http://localhost:5000
2025-07-13 02:44:46,657 - __main__ - INFO - [INFO] Use Ctrl+C to stop the application
2025-07-13 02:44:46,679 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:44:46,680 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
