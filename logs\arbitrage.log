2025-07-13 02:40:37,251 - __main__ - INFO - Background scanner started
2025-07-13 02:40:38,780 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:40:38,788 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:40:40,320 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:40:40,321 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:40:40,321 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:40:40,322 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:40:40,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:40:40,372 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:40:41,673 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:40:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:40:43,062 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:40:43] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:06,098 - __main__ - INFO - Background scanner started
2025-07-13 02:41:07,626 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:41:07,627 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:41:09,155 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:41:09,155 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:41:09,158 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:41:09,159 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:41:09,207 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:41:09,208 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:41:10,673 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:41:10,973 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:41:10,974 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:41:21,474 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:21] "GET / HTTP/1.1" 200 -
2025-07-13 02:41:25,756 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:25] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:27,392 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:27] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:27,394 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:27] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:41:28,206 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:28] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-13 02:41:38,026 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:38] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:41,411 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:41:41,719 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:41:41,720 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:41:44,294 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:44] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:48,984 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:48] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:58,059 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:58] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:41:58,363 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:41:58] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:42:02,217 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:02] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:08,368 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:08] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:12,047 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:42:12,365 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:42:12,365 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:42:18,652 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:18] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:27,696 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:27] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:27,700 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:27] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:42:38,002 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:38] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:42,696 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:42:42,999 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:42:43,000 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:42:47,689 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:47] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:42:57,685 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:57] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:42:57,995 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:42:57] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:08,971 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:08] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:13,340 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:43:13,655 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:43:13,656 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:43:18,046 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:18] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:28,361 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:28] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:43:29,080 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:29] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:38,050 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:38] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:44,006 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:43:44,314 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:43:44,315 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:43:49,021 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:49] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:58,059 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:58] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:43:58,368 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:43:58] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:44:07,362 - __main__ - INFO - [OK] All dependencies available
2025-07-13 02:44:07,365 - __main__ - INFO - [INFO] Testing API connections...
2025-07-13 02:44:09,194 - __main__ - WARNING - [WARN] Binance API returned status 451
2025-07-13 02:44:11,035 - __main__ - WARNING - [WARN] Bybit API returned status 403
2025-07-13 02:44:11,036 - __main__ - INFO - [START] Starting CryptoArb Pro...
2025-07-13 02:44:11,038 - __main__ - INFO - Background scanner started
2025-07-13 02:44:11,039 - __main__ - INFO - [SCAN] Performing initial scan...
2025-07-13 02:44:12,547 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:12,565 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:13,786 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:13,787 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:13,828 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:13,828 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:13,829 - __main__ - INFO - [RESULT] Initial scan found 0 opportunities
2025-07-13 02:44:13,829 - __main__ - INFO - [WEB] Starting web interface...
2025-07-13 02:44:13,831 - __main__ - INFO - [INFO] Access the application at: http://localhost:5000
2025-07-13 02:44:13,832 - __main__ - INFO - [INFO] Use Ctrl+C to stop the application
2025-07-13 02:44:13,856 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:44:13,857 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:44:28,360 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:44:28] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:44:40,502 - __main__ - INFO - [OK] All dependencies available
2025-07-13 02:44:40,502 - __main__ - INFO - [INFO] Testing API connections...
2025-07-13 02:44:41,677 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:44:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:44:42,064 - __main__ - WARNING - [WARN] Binance API returned status 451
2025-07-13 02:44:43,576 - __main__ - WARNING - [WARN] Bybit API returned status 403
2025-07-13 02:44:43,577 - __main__ - INFO - [START] Starting CryptoArb Pro...
2025-07-13 02:44:43,578 - __main__ - INFO - Background scanner started
2025-07-13 02:44:43,579 - __main__ - INFO - [SCAN] Performing initial scan...
2025-07-13 02:44:44,107 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:44,423 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:44,423 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:45,096 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:45,096 - __main__ - ERROR - Binance API error: 451 Client Error:  for url: https://api.binance.com/api/v3/ticker/24hr
2025-07-13 02:44:46,621 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:46,621 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:46,653 - __main__ - ERROR - Bybit API error: 403 Client Error: Forbidden for url: https://api.bybit.com/v5/market/tickers?category=spot
2025-07-13 02:44:46,655 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:44:46,655 - __main__ - INFO - [RESULT] Initial scan found 0 opportunities
2025-07-13 02:44:46,656 - __main__ - INFO - [WEB] Starting web interface...
2025-07-13 02:44:46,656 - __main__ - INFO - [INFO] Access the application at: http://localhost:5000
2025-07-13 02:44:46,657 - __main__ - INFO - [INFO] Use Ctrl+C to stop the application
2025-07-13 02:44:46,679 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:44:46,680 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:45:14,526 - __main__ - ERROR - Binance API error: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/ticker/24hr (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001B3004D5810>: Failed to resolve 'api.binance.com' ([Errno 11002] getaddrinfo failed)"))
2025-07-13 02:45:14,531 - __main__ - ERROR - Bybit API error: HTTPSConnectionPool(host='api.bybit.com', port=443): Max retries exceeded with url: /v5/market/tickers?category=spot (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001B3004D5450>: Failed to resolve 'api.bybit.com' ([Errno 11002] getaddrinfo failed)"))
2025-07-13 02:45:14,531 - __main__ - WARNING - Failed to fetch data from one or both exchanges
2025-07-13 02:45:41,462 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:45:41] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:45:44,220 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:45:44] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:45:47,184 - __main__ - INFO - Scan completed: 5 opportunities found in 2.63s
2025-07-13 02:46:19,213 - __main__ - INFO - Scan completed: 6 opportunities found in 1.91s
2025-07-13 02:46:23,492 - __main__ - INFO - [OK] All dependencies available
2025-07-13 02:46:23,492 - __main__ - INFO - [INFO] Testing API connections...
2025-07-13 02:46:24,655 - __main__ - INFO - [OK] Binance API connection successful
2025-07-13 02:46:25,584 - __main__ - INFO - [OK] Bybit API connection successful
2025-07-13 02:46:25,584 - __main__ - INFO - [START] Starting CryptoArb Pro...
2025-07-13 02:46:25,584 - __main__ - INFO - Background scanner started
2025-07-13 02:46:25,601 - __main__ - INFO - [SCAN] Performing initial scan...
2025-07-13 02:46:29,372 - __main__ - INFO - Scan completed: 7 opportunities found in 3.77s
2025-07-13 02:46:29,372 - __main__ - INFO - Scan completed: 7 opportunities found in 3.79s
2025-07-13 02:46:29,380 - __main__ - INFO - [RESULT] Initial scan found 7 opportunities
2025-07-13 02:46:29,380 - __main__ - INFO - [WEB] Starting web interface...
2025-07-13 02:46:29,380 - __main__ - INFO - [INFO] Access the application at: http://localhost:5000
2025-07-13 02:46:29,388 - __main__ - INFO - [INFO] Use Ctrl+C to stop the application
2025-07-13 02:46:29,675 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 02:46:29,680 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 02:46:30,282 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:46:30] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:46:30,704 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:46:30] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:46:37,696 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:46:37] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:46:48,334 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:46:48] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:46:50,694 - __main__ - INFO - Scan completed: 5 opportunities found in 1.35s
2025-07-13 02:46:57,677 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:46:57] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:46:57,680 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:46:57] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:47:01,029 - __main__ - INFO - Scan completed: 5 opportunities found in 1.60s
2025-07-13 02:47:08,887 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:47:08] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:47:18,352 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:47:18] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:47:22,051 - __main__ - INFO - Scan completed: 6 opportunities found in 1.33s
2025-07-13 02:47:28,348 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:47:28] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:47:28,902 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:47:28] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:47:35,942 - __main__ - INFO - Scan completed: 5 opportunities found in 4.75s
2025-07-13 02:47:43,625 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:47:43] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:47:48,058 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:47:48] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:47:54,640 - __main__ - INFO - Scan completed: 5 opportunities found in 2.24s
2025-07-13 02:47:58,358 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:47:58] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:47:59,200 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:47:59] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:48:09,181 - __main__ - INFO - Scan completed: 5 opportunities found in 3.02s
2025-07-13 02:48:26,428 - __main__ - INFO - Scan completed: 5 opportunities found in 1.77s
2025-07-13 02:48:40,567 - __main__ - INFO - Scan completed: 5 opportunities found in 1.34s
2025-07-13 02:48:41,369 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:48:41] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:48:41,898 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:48:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:48:57,782 - __main__ - INFO - Scan completed: 6 opportunities found in 1.31s
2025-07-13 02:49:11,708 - __main__ - INFO - Scan completed: 5 opportunities found in 1.10s
2025-07-13 02:49:14,778 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:14] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:49:15,583 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:15] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:49:17,381 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:17] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:49:18,491 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:18] "GET / HTTP/1.1" 200 -
2025-07-13 02:49:22,029 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:22] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:49:22,032 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:22] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:49:28,898 - __main__ - INFO - Scan completed: 6 opportunities found in 1.09s
2025-07-13 02:49:33,164 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:33] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:49:42,145 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:42] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:49:45,593 - __main__ - INFO - Scan completed: 5 opportunities found in 3.79s
2025-07-13 02:49:55,895 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:55] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:49:56,219 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:56] "GET / HTTP/1.1" 200 -
2025-07-13 02:49:56,749 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:49:56] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:06,047 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:06] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:06,651 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:06] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:50:07,284 - __main__ - INFO - Scan completed: 6 opportunities found in 7.20s
2025-07-13 02:50:09,863 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:09] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:17,811 - __main__ - INFO - Scan completed: 5 opportunities found in 1.71s
2025-07-13 02:50:21,032 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:21] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:21,038 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:21] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:22,731 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:22] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:50:22,773 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:22] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:23,150 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:23] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:33,261 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:33] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:50:33,793 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:33] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:34,177 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:34] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:43,506 - __main__ - INFO - Scan completed: 5 opportunities found in 2.64s
2025-07-13 02:50:45,492 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:45] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:46,637 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:46] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:51,981 - __main__ - INFO - Scan completed: 5 opportunities found in 2.11s
2025-07-13 02:50:52,584 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:52] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:50:52,590 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:50:52] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:51:03,314 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:03] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:51:08,731 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:08] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:51:08,745 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:08] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 02:51:10,937 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:10] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:51:12,351 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 02:51:12] "GET /api/status HTTP/1.1" 200 -
2025-07-13 02:51:15,157 - __main__ - INFO - Scan completed: 7 opportunities found in 1.23s
2025-07-13 03:06:32,170 - __main__ - INFO - [OK] All dependencies available
2025-07-13 03:06:32,172 - __main__ - INFO - [INFO] Testing API connections...
2025-07-13 03:06:33,616 - __main__ - INFO - [OK] Binance API connection successful
2025-07-13 03:06:35,188 - __main__ - INFO - [OK] Bybit API connection successful
2025-07-13 03:06:35,189 - __main__ - INFO - [START] Starting CryptoArb Pro...
2025-07-13 03:06:35,191 - __main__ - INFO - Background scanner started
2025-07-13 03:06:35,192 - __main__ - INFO - [SCAN] Performing initial scan...
2025-07-13 03:06:39,075 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 3.88s
2025-07-13 03:06:39,339 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 4.15s
2025-07-13 03:06:39,346 - __main__ - INFO - [RESULT] Initial scan found 0 opportunities
2025-07-13 03:06:39,347 - __main__ - INFO - [WEB] Starting web interface...
2025-07-13 03:06:39,348 - __main__ - INFO - [INFO] Access the application at: http://localhost:5000
2025-07-13 03:06:39,358 - __main__ - INFO - [INFO] Use Ctrl+C to stop the application
2025-07-13 03:06:39,391 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://********:5000
2025-07-13 03:06:39,392 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 03:06:46,028 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:06:46] "GET / HTTP/1.1" 200 -
2025-07-13 03:06:50,376 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:06:50] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 03:06:51,256 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:06:51] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:07:00,695 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:00] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:07:10,573 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.40s
2025-07-13 03:07:11,915 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:11] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:07:21,367 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:21] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:07:21,368 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:21] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 03:07:31,898 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:31] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:07:40,678 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:40] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:07:42,173 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.54s
2025-07-13 03:07:45,370 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.36s
2025-07-13 03:07:45,373 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:45] "GET /api/scan HTTP/1.1" 200 -
2025-07-13 03:07:45,691 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:45] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 03:07:51,361 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:51] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 03:07:51,889 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:07:51] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:08:01,360 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:08:01] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:08:11,908 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:08:11] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:08:13,578 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.36s
2025-07-13 03:08:21,558 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:08:21] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 03:08:22,372 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:08:22] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:08:31,054 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:08:31] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:08:41,549 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:08:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:08:45,151 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.52s
2025-07-13 03:08:50,387 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:08:50] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:08:50,704 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:08:50] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 03:09:01,526 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:09:01] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:09:02,214 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.88s
2025-07-13 03:09:02,221 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:09:02] "GET /api/scan HTTP/1.1" 200 -
2025-07-13 03:09:02,233 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:09:02] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 03:09:10,707 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:09:10] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:09:16,652 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.47s
2025-07-13 03:09:21,888 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:09:21] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:09:31,358 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:09:31] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:09:41,869 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:09:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:09:47,922 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.24s
2025-07-13 03:09:50,687 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:09:50] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:10:01,888 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:10:01] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:10:11,365 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:10:11] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:10:19,336 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.39s
2025-07-13 03:10:21,890 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:10:21] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:10:31,370 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:10:31] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:10:41,899 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:10:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:10:50,987 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.64s
2025-07-13 03:10:51,350 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:10:51] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:11:23,265 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 2.20s
2025-07-13 03:11:41,886 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:11:41] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:11:54,375 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.09s
2025-07-13 03:12:25,518 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.11s
2025-07-13 03:12:41,434 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:12:41] "GET /api/opportunities?min_profit=0.5&max_profit=200&min_volume=10000&network= HTTP/1.1" 200 -
2025-07-13 03:12:42,280 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:12:42] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:12:42,284 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 03:12:42] "GET /api/status HTTP/1.1" 200 -
2025-07-13 03:12:57,244 - __main__ - INFO - Enhanced scan completed: 0 validated opportunities found in 1.71s
2025-07-13 03:13:16,244 - __main__ - INFO - [CLEANUP] Cleaning up...
2025-07-13 03:13:16,259 - __main__ - INFO - Background scanner stopped
2025-07-13 03:13:16,259 - __main__ - INFO - [OK] CryptoArb Pro stopped successfully
