# 🚀 CryptoArb Pro - Deteksi Arbitrase Cryptocurrency

Program deteksi peluang arbitrase cryptocurrency real-time antara Binance dan Bybit dengan interface web yang modern dan futuristik.

## ✨ Fitur Utama

### 🎯 Deteksi Arbitrase Real-time
- **300+ Trading Pairs**: Mencakup semua pasangan populer (BTC/USDT, ETH/USDT, dll)
- **Update Setiap 30 Detik**: Data harga real-time dari kedua bursa
- **Profit Range**: Filter peluang 0.5% - 200% untuk menghindari false signals
- **Volume Validation**: Minimum $10,000 volume 24 jam untuk memastikan likuiditas

### 🌐 Interface Modern
- **Dark Futuristic Theme**: Glassmorphism effects dengan neon accents
- **Real-time Status**: Indikator kesehatan API dengan auto-reconnect
- **Responsive Design**: Optimal untuk desktop dan tablet
- **Indonesian Localization**: UI lengkap dalam bahasa Indonesia

### 🔧 Fitur Advanced
- **Filter System**: Profit range, volume minimum, network selection
- **Trading Simulation**: Kalkulator profit dengan slippage estimation
- **Notification System**: Toast alerts untuk peluang profit tinggi (>5%)
- **Analytics Dashboard**: Statistics dan performance metrics
- **Direct Trading Links**: Link langsung ke halaman trading Binance/Bybit

### ⚡ Performance & Reliability
- **High Performance**: 20+ tokens/second, memory usage <2GB
- **Rate Limiting**: Compliance dengan API limits (Binance: 1200/min, Bybit: 120/min)
- **Error Handling**: Comprehensive error handling dengan auto-recovery
- **Health Monitoring**: Real-time API status monitoring

## 🛠️ Instalasi

### Prerequisites
- Python 3.8+
- Conda (recommended)
- Windows 11 (tested environment)

### Setup Environment
```bash
# Clone atau download project
cd "C:\Users\<USER>\project 4"

# Install dependencies
pip install -r requirements.txt

# Atau menggunakan conda
conda install flask requests
```

### Menjalankan Program
```bash
python main.py
```

Program akan tersedia di: http://localhost:5000

## 📊 Cara Penggunaan

### 1. Monitoring Status
- **Indikator Hijau**: API terhubung normal
- **Indikator Merah**: API bermasalah
- **Indikator Kuning**: Loading/reconnecting

### 2. Filter Peluang
- **Profit Range**: Sesuaikan minimum dan maksimum profit
- **Volume Filter**: Set minimum volume untuk likuiditas
- **Network Filter**: Pilih jaringan blockchain tertentu

### 3. Analisis Peluang
- **Klik baris tabel**: Lihat detail lengkap peluang
- **Trading Links**: Akses langsung ke halaman trading
- **Profit Calculator**: Hitung estimasi profit berdasarkan investasi

### 4. Auto Scan
- **Toggle Auto Scan**: Aktifkan/nonaktifkan pemindaian otomatis
- **Manual Scan**: Trigger scan manual dengan tombol atau F5
- **Real-time Updates**: Data diperbarui setiap 30 detik

## 🔍 Algoritma Arbitrase

### Perhitungan Profit
```
Profit (%) = ((Harga_Tinggi - Harga_Rendah) / Harga_Rendah) * 100
Profit Bersih = Profit Kotor - (Trading Fee * 2)
```

### Validasi Peluang
1. **Volume Check**: Minimum $10,000 volume 24 jam
2. **Price Validation**: Harga positif dan valid
3. **Profit Range**: 0.5% - 200% untuk menghindari anomali
4. **Network Compatibility**: Validasi jaringan transfer yang didukung

### Arah Trading
- **Binance → Bybit**: Beli di Binance, jual di Bybit
- **Bybit → Binance**: Beli di Bybit, jual di Binance

## 🌐 API Integration

### Binance API
- **Endpoint**: https://api.binance.com/api/v3/
- **Rate Limit**: 1200 requests/minute
- **Data**: 24hr ticker, exchange info

### Bybit API  
- **Endpoint**: https://api.bybit.com/v5/
- **Rate Limit**: 120 requests/minute
- **Data**: Spot market tickers

### Network Support
- **Ethereum (ETH)**: Transfer time 5-15 menit
- **Binance Smart Chain (BSC)**: Transfer time 1-3 menit
- **Tron (TRC20)**: Transfer time 1-5 menit
- **Polygon**: Transfer time 1-2 menit
- **Arbitrum**: Transfer time 1-5 menit

## 📈 Performance Metrics

### Target Specifications
- **Scan Speed**: 20+ tokens per second
- **Memory Usage**: <2GB RAM
- **Response Time**: <3 seconds
- **Update Frequency**: 30 seconds
- **Token Coverage**: 300+ trading pairs

### Optimization Features
- **Parallel Processing**: Multi-threaded scanning
- **Data Caching**: Reduce API calls
- **Connection Pooling**: Efficient HTTP connections
- **Rate Limiting**: Prevent API throttling

## 🔒 Keamanan & Validasi

### Data Validation
- **Input Sanitization**: Semua user input divalidasi
- **Price Validation**: Harga positif dan dalam range wajar
- **Volume Validation**: Volume minimum untuk likuiditas
- **Symbol Validation**: Format symbol yang benar

### Error Handling
- **Network Timeouts**: Auto-retry dengan exponential backoff
- **API Rate Limits**: Intelligent rate limiting
- **Invalid Responses**: Data validation dan fallback
- **Connection Issues**: Auto-reconnect mechanism

## 📝 Logging & Monitoring

### Log Levels
- **INFO**: Operasi normal, scan results
- **WARNING**: API issues, rate limiting
- **ERROR**: Connection failures, data errors

### Log Files
- **Location**: `logs/arbitrage.log`
- **Rotation**: 10MB per file, 5 backup files
- **Format**: Timestamp, level, message

## 🚨 Risk Disclaimer

**PERINGATAN PENTING:**
- Program ini hanya untuk deteksi peluang, bukan untuk trading otomatis
- Selalu verifikasi data sebelum melakukan trading
- Pertimbangkan slippage, network fees, dan market volatility
- Trading cryptocurrency memiliki risiko tinggi
- Gunakan hanya dana yang siap Anda rugikan

## 👨‍💻 Author & Support

**Author**: BOBACHEESE  
**Framework**: Flask + HTML/CSS/JavaScript  
**Environment**: Conda on Windows 11  
**Version**: 1.0.0

### Social Media
- **GitHub**: https://github.com/bobacheese
- **Twitter**: https://twitter.com/bobacheese
- **Telegram**: https://t.me/bobacheese

## 📄 License

MIT License - Bebas digunakan untuk tujuan edukasi dan komersial.

---

**🙏 Terima kasih telah menggunakan CryptoArb Pro!**  
**💡 Happy Trading & Stay Safe! 🚀**
