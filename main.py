#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CryptoArb Pro - Deteksi Peluang Arbitrase Cryptocurrency
Binance vs Bybit Real-time Arbitrage Detection System

Author: BOBACHEESE
Version: 1.0.0
Framework: Flask + HTML/CSS/JavaScript
Target: 300+ trading pairs, <2GB memory, 20+ tokens/second
"""

import logging
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional
import requests
from flask import Flask, render_template_string, jsonify, request
import concurrent.futures
from dataclasses import dataclass

# Konfigurasi Logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('arbitrage.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ArbitrageOpportunity:
    """Data class untuk peluang arbitrase"""
    symbol: str
    binance_price: float
    bybit_price: float
    profit_percentage: float
    volume_24h: float
    binance_volume: float
    bybit_volume: float
    direction: str  # 'binance_to_bybit' atau 'bybit_to_binance'
    timestamp: datetime
    networks: List[str]
    trading_fee: float = 0.1  # 0.1% per transaksi
    
    @property
    def net_profit(self) -> float:
        """Profit bersih setelah trading fee"""
        return max(0, self.profit_percentage - (self.trading_fee * 2))

class RateLimiter:
    """Rate limiter untuk API calls"""
    def __init__(self, max_calls: int, time_window: int):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
        self.lock = threading.Lock()
    
    def can_make_call(self) -> bool:
        with self.lock:
            now = time.time()
            # Hapus calls yang sudah expired
            self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
            
            if len(self.calls) < self.max_calls:
                self.calls.append(now)
                return True
            return False
    
    def wait_time(self) -> float:
        """Waktu tunggu sebelum bisa melakukan call berikutnya"""
        with self.lock:
            if len(self.calls) < self.max_calls:
                return 0
            oldest_call = min(self.calls)
            return max(0, self.time_window - (time.time() - oldest_call))

class BinanceClient:
    """Client untuk Binance API"""
    def __init__(self):
        self.base_url = "https://api.binance.com/api/v3"
        self.rate_limiter = RateLimiter(1200, 60)  # 1200 requests per minute
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'CryptoArb-Pro/1.0',
            'Content-Type': 'application/json'
        })
        self.last_health_check = 0
        self.is_healthy = False
    
    def health_check(self) -> bool:
        """Cek status kesehatan API"""
        try:
            if time.time() - self.last_health_check < 10:
                return self.is_healthy
            
            response = self.session.get(f"{self.base_url}/ping", timeout=5)
            self.is_healthy = response.status_code == 200
            self.last_health_check = time.time()
            return self.is_healthy
        except Exception as e:
            logger.error(f"Binance health check failed: {e}")
            self.is_healthy = False
            return False
    
    def get_24hr_ticker(self) -> Optional[List[Dict]]:
        """Ambil data ticker 24 jam"""
        if not self.rate_limiter.can_make_call():
            wait_time = self.rate_limiter.wait_time()
            if wait_time > 0:
                time.sleep(wait_time)
        
        try:
            response = self.session.get(f"{self.base_url}/ticker/24hr", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Binance API error: {e}")
            return None
    
    def get_exchange_info(self) -> Optional[Dict]:
        """Ambil informasi exchange"""
        try:
            response = self.session.get(f"{self.base_url}/exchangeInfo", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Binance exchange info error: {e}")
            return None

class BybitClient:
    """Client untuk Bybit API"""
    def __init__(self):
        self.base_url = "https://api.bybit.com/v5"
        self.rate_limiter = RateLimiter(120, 60)  # 120 requests per minute
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'CryptoArb-Pro/1.0',
            'Content-Type': 'application/json'
        })
        self.last_health_check = 0
        self.is_healthy = False
    
    def health_check(self) -> bool:
        """Cek status kesehatan API"""
        try:
            if time.time() - self.last_health_check < 10:
                return self.is_healthy
            
            response = self.session.get(f"{self.base_url}/market/time", timeout=5)
            self.is_healthy = response.status_code == 200
            self.last_health_check = time.time()
            return self.is_healthy
        except Exception as e:
            logger.error(f"Bybit health check failed: {e}")
            self.is_healthy = False
            return False
    
    def get_tickers(self) -> Optional[Dict]:
        """Ambil data ticker spot market"""
        if not self.rate_limiter.can_make_call():
            wait_time = self.rate_limiter.wait_time()
            if wait_time > 0:
                time.sleep(wait_time)
        
        try:
            params = {'category': 'spot'}
            response = self.session.get(f"{self.base_url}/market/tickers", params=params, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Bybit API error: {e}")
            return None

class ArbitrageEngine:
    """Engine utama untuk deteksi arbitrase"""
    def __init__(self):
        self.binance_client = BinanceClient()
        self.bybit_client = BybitClient()
        self.opportunities = []
        self.last_update = None
        self.stats = {
            'total_scanned': 0,
            'opportunities_found': 0,
            'average_profit': 0,
            'scan_duration': 0
        }
        
        # Target symbols prioritas
        self.priority_symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'ADAUSDT',
            'DOGEUSDT', 'XRPUSDT', 'MATICUSDT', 'AVAXUSDT', 'DOTUSDT',
            'LINKUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT', 'LTCUSDT',
            'TRXUSDT', 'NEARUSDT', 'APTUSDT', 'OPUSDT', 'ARBUSDT'
        ]
        
        # Cache untuk data
        self.binance_cache = {}
        self.bybit_cache = {}
        self.cache_expiry = 30  # 30 detik
    
    def get_common_symbols(self, binance_data: List[Dict], bybit_data: Dict) -> List[str]:
        """Dapatkan symbol yang tersedia di kedua bursa"""
        binance_symbols = {item['symbol'] for item in binance_data}
        bybit_symbols = {item['symbol'] for item in bybit_data.get('result', {}).get('list', [])}
        
        common = binance_symbols.intersection(bybit_symbols)
        
        # Prioritaskan symbol yang ada di priority list
        priority_common = [s for s in self.priority_symbols if s in common]
        other_common = [s for s in common if s not in self.priority_symbols]
        
        return priority_common + other_common[:280]  # Total 300 symbols max
    
    def calculate_arbitrage(self, symbol: str, binance_price: float, bybit_price: float,
                          binance_volume: float, bybit_volume: float) -> Optional[ArbitrageOpportunity]:
        """Hitung peluang arbitrase"""
        if binance_price <= 0 or bybit_price <= 0:
            return None
        
        # Minimum volume requirement ($10,000)
        min_volume = 10000
        if binance_volume < min_volume or bybit_volume < min_volume:
            return None
        
        # Hitung profit percentage
        if binance_price > bybit_price:
            profit_pct = ((binance_price - bybit_price) / bybit_price) * 100
            direction = 'bybit_to_binance'
        else:
            profit_pct = ((bybit_price - binance_price) / binance_price) * 100
            direction = 'binance_to_bybit'
        
        # Filter profit range (0.5% - 200%)
        if profit_pct < 0.5 or profit_pct > 200:
            return None
        
        # Networks yang umum didukung
        networks = ['ETH', 'BSC', 'TRC20', 'POLYGON', 'ARBITRUM']
        
        return ArbitrageOpportunity(
            symbol=symbol,
            binance_price=binance_price,
            bybit_price=bybit_price,
            profit_percentage=profit_pct,
            volume_24h=min(binance_volume, bybit_volume),
            binance_volume=binance_volume,
            bybit_volume=bybit_volume,
            direction=direction,
            timestamp=datetime.now(),
            networks=networks
        )

    def scan_opportunities(self) -> List[ArbitrageOpportunity]:
        """Scan peluang arbitrase"""
        start_time = time.time()
        opportunities = []

        try:
            # Ambil data dari kedua bursa
            binance_data = self.binance_client.get_24hr_ticker()
            bybit_data = self.bybit_client.get_tickers()

            if not binance_data or not bybit_data:
                logger.warning("Failed to fetch data from one or both exchanges")
                return opportunities

            # Konversi data untuk processing
            binance_dict = {item['symbol']: item for item in binance_data}
            bybit_dict = {item['symbol']: item for item in bybit_data.get('result', {}).get('list', [])}

            common_symbols = self.get_common_symbols(binance_data, bybit_data)
            self.stats['total_scanned'] = len(common_symbols)

            # Parallel processing untuk performa
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                futures = []

                for symbol in common_symbols:
                    if symbol in binance_dict and symbol in bybit_dict:
                        future = executor.submit(self._process_symbol, symbol, binance_dict[symbol], bybit_dict[symbol])
                        futures.append(future)

                for future in concurrent.futures.as_completed(futures):
                    try:
                        opportunity = future.result()
                        if opportunity:
                            opportunities.append(opportunity)
                    except Exception as e:
                        logger.error(f"Error processing symbol: {e}")

            # Sort berdasarkan profit percentage (tertinggi ke terendah)
            opportunities.sort(key=lambda x: x.profit_percentage, reverse=True)

            # Update statistics
            self.opportunities = opportunities
            self.last_update = datetime.now()
            self.stats['opportunities_found'] = len(opportunities)
            self.stats['average_profit'] = sum(op.profit_percentage for op in opportunities) / len(opportunities) if opportunities else 0
            self.stats['scan_duration'] = time.time() - start_time

            logger.info(f"Scan completed: {len(opportunities)} opportunities found in {self.stats['scan_duration']:.2f}s")

        except Exception as e:
            logger.error(f"Scan error: {e}")

        return opportunities

    def _process_symbol(self, symbol: str, binance_item: Dict, bybit_item: Dict) -> Optional[ArbitrageOpportunity]:
        """Process single symbol untuk arbitrase"""
        try:
            # Parse Binance data
            binance_price = float(binance_item.get('lastPrice', 0))
            binance_volume = float(binance_item.get('quoteVolume', 0))

            # Parse Bybit data
            bybit_price = float(bybit_item.get('lastPrice', 0))
            bybit_volume = float(bybit_item.get('turnover24h', 0))

            return self.calculate_arbitrage(symbol, binance_price, bybit_price, binance_volume, bybit_volume)

        except Exception as e:
            logger.error(f"Error processing {symbol}: {e}")
            return None

    def get_status(self) -> Dict:
        """Status sistem"""
        return {
            'binance_healthy': self.binance_client.health_check(),
            'bybit_healthy': self.bybit_client.health_check(),
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'opportunities_count': len(self.opportunities),
            'stats': self.stats
        }

# Flask Application
app = Flask(__name__)
app.config['SECRET_KEY'] = 'cryptoarb-pro-2024'

# Global arbitrage engine
arbitrage_engine = ArbitrageEngine()

@app.route('/')
def index():
    """Halaman utama"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/opportunities')
def get_opportunities():
    """API endpoint untuk peluang arbitrase"""
    try:
        # Filter berdasarkan parameter
        min_profit = float(request.args.get('min_profit', 0.5))
        max_profit = float(request.args.get('max_profit', 200))
        min_volume = float(request.args.get('min_volume', 10000))

        filtered_opportunities = []
        for op in arbitrage_engine.opportunities:
            if (min_profit <= op.profit_percentage <= max_profit and
                op.volume_24h >= min_volume):
                filtered_opportunities.append({
                    'symbol': op.symbol,
                    'binance_price': op.binance_price,
                    'bybit_price': op.bybit_price,
                    'profit_percentage': round(op.profit_percentage, 3),
                    'net_profit': round(op.net_profit, 3),
                    'volume_24h': op.volume_24h,
                    'direction': op.direction,
                    'timestamp': op.timestamp.isoformat(),
                    'networks': op.networks,
                    'binance_link': f"https://www.binance.com/en/trade/{op.symbol}",
                    'bybit_link': f"https://www.bybit.com/en/trade/spot/{op.symbol}"
                })

        return jsonify({
            'success': True,
            'data': filtered_opportunities,
            'count': len(filtered_opportunities)
        })

    except Exception as e:
        logger.error(f"API error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/status')
def get_status():
    """API endpoint untuk status sistem"""
    return jsonify(arbitrage_engine.get_status())

@app.route('/api/scan')
def trigger_scan():
    """Trigger manual scan"""
    try:
        opportunities = arbitrage_engine.scan_opportunities()
        return jsonify({
            'success': True,
            'opportunities_found': len(opportunities),
            'scan_time': arbitrage_engine.stats['scan_duration']
        })
    except Exception as e:
        logger.error(f"Manual scan error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# HTML Template dengan Dark Futuristic Theme
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoArb Pro - Deteksi Arbitrase Cryptocurrency</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 8px 32px rgba(0, 255, 136, 0.1);
        }

        .logo {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff88, #3742fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(0, 255, 136, 0.5);
        }

        .status-indicators {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.healthy { background: #00ff88; }
        .status-dot.unhealthy { background: #ff4757; }
        .status-dot.loading { background: #ffa502; }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 255, 136, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 255, 136, 0); }
        }

        .current-time {
            font-size: 14px;
            color: #a4a4a4;
        }

        /* Main Dashboard */
        .dashboard {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        /* Sidebar Filters */
        .sidebar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
            height: fit-content;
        }

        .sidebar h3 {
            color: #00ff88;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .filter-group {
            margin-bottom: 25px;
        }

        .filter-group label {
            display: block;
            margin-bottom: 8px;
            color: #a4a4a4;
            font-size: 14px;
        }

        .filter-input {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
        }

        .filter-input:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }

        .range-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
        }

        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #00ff88;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .btn {
            background: linear-gradient(45deg, #00ff88, #3742fa);
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 15px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 255, 136, 0.3);
        }

        /* Main Content */
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .content-title {
            font-size: 24px;
            color: #00ff88;
        }

        .scan-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .auto-scan-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 25px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: #00ff88;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .toggle-switch.active::after {
            transform: translateX(25px);
        }

        /* Opportunities Table */
        .opportunities-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .opportunities-table th,
        .opportunities-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .opportunities-table th {
            background: rgba(0, 255, 136, 0.1);
            color: #00ff88;
            font-weight: bold;
            position: sticky;
            top: 0;
        }

        .opportunities-table tr {
            transition: all 0.3s ease;
        }

        .opportunities-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: scale(1.01);
        }

        .profit-positive {
            color: #00ff88;
            font-weight: bold;
        }

        .profit-high {
            color: #00ff88;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
            animation: glow 2s infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 10px rgba(0, 255, 136, 0.5); }
            to { text-shadow: 0 0 20px rgba(0, 255, 136, 0.8); }
        }

        .direction-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .direction-binance {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .direction-bybit {
            background: rgba(255, 87, 87, 0.2);
            color: #ff5757;
            border: 1px solid rgba(255, 87, 87, 0.3);
        }

        .exchange-links {
            display: flex;
            gap: 10px;
        }

        .exchange-link {
            padding: 6px 12px;
            background: rgba(55, 66, 250, 0.2);
            color: #3742fa;
            text-decoration: none;
            border-radius: 15px;
            font-size: 12px;
            border: 1px solid rgba(55, 66, 250, 0.3);
            transition: all 0.3s ease;
        }

        .exchange-link:hover {
            background: rgba(55, 66, 250, 0.3);
            transform: translateY(-2px);
        }

        /* Loading States */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 50px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left: 4px solid #00ff88;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Footer Stats */
        .footer-stats {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #a4a4a4;
            font-size: 14px;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            margin: 5% auto;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .close {
            color: #a4a4a4;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #ff4757;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .status-indicators {
                flex-wrap: wrap;
                justify-content: center;
            }

            .opportunities-table {
                font-size: 12px;
            }

            .opportunities-table th,
            .opportunities-table td {
                padding: 8px;
            }
        }

        /* Toast Notifications */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 255, 136, 0.9);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1001;
        }

        .toast.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🚀 CryptoArb Pro</div>
            <div class="status-indicators">
                <div class="status-indicator">
                    <div class="status-dot loading" id="binance-status"></div>
                    <span>Binance</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot loading" id="bybit-status"></div>
                    <span>Bybit</span>
                </div>
                <div class="current-time" id="current-time"></div>
            </div>
        </div>

        <!-- Dashboard -->
        <div class="dashboard">
            <!-- Sidebar Filters -->
            <div class="sidebar">
                <h3>🎛️ Filter Peluang</h3>

                <div class="filter-group">
                    <label for="min-profit">Profit Minimum (%)</label>
                    <input type="range" id="min-profit" class="range-slider" min="0.5" max="50" value="0.5" step="0.1">
                    <span id="min-profit-value">0.5%</span>
                </div>

                <div class="filter-group">
                    <label for="max-profit">Profit Maksimum (%)</label>
                    <input type="range" id="max-profit" class="range-slider" min="1" max="200" value="200" step="1">
                    <span id="max-profit-value">200%</span>
                </div>

                <div class="filter-group">
                    <label for="min-volume">Volume Minimum ($)</label>
                    <input type="number" id="min-volume" class="filter-input" value="10000" min="1000" step="1000">
                </div>

                <div class="filter-group">
                    <label for="network-filter">Jaringan</label>
                    <select id="network-filter" class="filter-input">
                        <option value="">Semua Jaringan</option>
                        <option value="ETH">Ethereum</option>
                        <option value="BSC">Binance Smart Chain</option>
                        <option value="TRC20">Tron</option>
                        <option value="POLYGON">Polygon</option>
                        <option value="ARBITRUM">Arbitrum</option>
                    </select>
                </div>

                <button class="btn" onclick="applyFilters()">🔍 Terapkan Filter</button>
                <button class="btn" onclick="triggerManualScan()">🔄 Scan Manual</button>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="content-header">
                    <h2 class="content-title">📊 Peluang Arbitrase Real-time</h2>
                    <div class="scan-controls">
                        <div class="auto-scan-toggle">
                            <span>Auto Scan</span>
                            <div class="toggle-switch active" id="auto-scan-toggle" onclick="toggleAutoScan()"></div>
                        </div>
                        <span id="last-update">Belum ada data</span>
                    </div>
                </div>

                <div id="opportunities-container">
                    <div class="loading">
                        <div class="spinner"></div>
                        <span style="margin-left: 15px;">Memuat peluang arbitrase...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Stats -->
        <div class="footer-stats">
            <div class="stat-item">
                <div class="stat-value" id="total-opportunities">0</div>
                <div class="stat-label">Total Peluang</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="average-profit">0%</div>
                <div class="stat-label">Rata-rata Profit</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="scan-duration">0s</div>
                <div class="stat-label">Waktu Scan</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="tokens-scanned">0</div>
                <div class="stat-label">Token Dipindai</div>
            </div>
        </div>
    </div>

    <!-- Modal Detail -->
    <div id="detail-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modal-content"></div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="toast"></div>

    <script>
        let autoScanEnabled = true;
        let scanInterval;
        let currentFilters = {
            min_profit: 0.5,
            max_profit: 200,
            min_volume: 10000,
            network: ''
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            checkSystemStatus();
            setInterval(checkSystemStatus, 10000);

            loadOpportunities();
            startAutoScan();

            // Setup filter event listeners
            setupFilterListeners();
        });

        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('current-time').textContent =
                now.toLocaleString('id-ID', {
                    timeZone: 'Asia/Jakarta',
                    hour12: false
                });
        }

        function setupFilterListeners() {
            const minProfitSlider = document.getElementById('min-profit');
            const maxProfitSlider = document.getElementById('max-profit');

            minProfitSlider.addEventListener('input', function() {
                document.getElementById('min-profit-value').textContent = this.value + '%';
                currentFilters.min_profit = parseFloat(this.value);
            });

            maxProfitSlider.addEventListener('input', function() {
                document.getElementById('max-profit-value').textContent = this.value + '%';
                currentFilters.max_profit = parseFloat(this.value);
            });
        }

        async function checkSystemStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();

                // Update status indicators
                const binanceStatus = document.getElementById('binance-status');
                const bybitStatus = document.getElementById('bybit-status');

                binanceStatus.className = 'status-dot ' + (data.binance_healthy ? 'healthy' : 'unhealthy');
                bybitStatus.className = 'status-dot ' + (data.bybit_healthy ? 'healthy' : 'unhealthy');

                // Update stats
                if (data.stats) {
                    document.getElementById('tokens-scanned').textContent = data.stats.total_scanned || 0;
                    document.getElementById('scan-duration').textContent = (data.stats.scan_duration || 0).toFixed(2) + 's';
                }

            } catch (error) {
                console.error('Status check failed:', error);
                document.getElementById('binance-status').className = 'status-dot unhealthy';
                document.getElementById('bybit-status').className = 'status-dot unhealthy';
            }
        }

        async function loadOpportunities() {
            try {
                const params = new URLSearchParams(currentFilters);
                const response = await fetch(`/api/opportunities?${params}`);
                const data = await response.json();

                if (data.success) {
                    displayOpportunities(data.data);
                    updateStats(data.data);

                    // Check for high profit opportunities (>5%)
                    const highProfitOps = data.data.filter(op => op.profit_percentage > 5);
                    if (highProfitOps.length > 0) {
                        showToast(`🚨 ${highProfitOps.length} peluang profit tinggi ditemukan!`);
                    }
                } else {
                    showToast('❌ Gagal memuat peluang: ' + data.error, 'error');
                }

            } catch (error) {
                console.error('Load opportunities failed:', error);
                showToast('❌ Koneksi terputus', 'error');
            }
        }

        function displayOpportunities(opportunities) {
            const container = document.getElementById('opportunities-container');

            if (opportunities.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 50px; color: #a4a4a4;">
                        <h3>📭 Tidak ada peluang arbitrase ditemukan</h3>
                        <p>Coba sesuaikan filter atau tunggu scan berikutnya</p>
                    </div>
                `;
                return;
            }

            let tableHTML = `
                <table class="opportunities-table">
                    <thead>
                        <tr>
                            <th>💰 Symbol</th>
                            <th>📈 Profit</th>
                            <th>💵 Harga Binance</th>
                            <th>💵 Harga Bybit</th>
                            <th>📊 Volume 24h</th>
                            <th>🔄 Arah</th>
                            <th>🔗 Trading</th>
                            <th>ℹ️ Detail</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            opportunities.forEach(op => {
                const profitClass = op.profit_percentage > 5 ? 'profit-high' : 'profit-positive';
                const directionClass = op.direction === 'binance_to_bybit' ? 'direction-binance' : 'direction-bybit';
                const directionText = op.direction === 'binance_to_bybit' ? 'Binance → Bybit' : 'Bybit → Binance';

                tableHTML += `
                    <tr onclick="showDetail('${op.symbol}', ${JSON.stringify(op).replace(/"/g, '&quot;')})">
                        <td><strong>${op.symbol}</strong></td>
                        <td class="${profitClass}">${op.profit_percentage.toFixed(3)}%</td>
                        <td>$${op.binance_price.toFixed(6)}</td>
                        <td>$${op.bybit_price.toFixed(6)}</td>
                        <td>$${formatNumber(op.volume_24h)}</td>
                        <td><span class="direction-badge ${directionClass}">${directionText}</span></td>
                        <td class="exchange-links">
                            <a href="${op.binance_link}" target="_blank" class="exchange-link">Binance</a>
                            <a href="${op.bybit_link}" target="_blank" class="exchange-link">Bybit</a>
                        </td>
                        <td><button onclick="event.stopPropagation(); showDetail('${op.symbol}', ${JSON.stringify(op).replace(/"/g, '&quot;')})" class="btn" style="padding: 5px 10px; margin: 0;">Detail</button></td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            container.innerHTML = tableHTML;

            // Update last update time
            document.getElementById('last-update').textContent =
                'Terakhir: ' + new Date().toLocaleTimeString('id-ID');
        }

        function updateStats(opportunities) {
            document.getElementById('total-opportunities').textContent = opportunities.length;

            if (opportunities.length > 0) {
                const avgProfit = opportunities.reduce((sum, op) => sum + op.profit_percentage, 0) / opportunities.length;
                document.getElementById('average-profit').textContent = avgProfit.toFixed(2) + '%';
            } else {
                document.getElementById('average-profit').textContent = '0%';
            }
        }

        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toFixed(0);
        }

        function showDetail(symbol, opportunity) {
            const modal = document.getElementById('detail-modal');
            const content = document.getElementById('modal-content');

            content.innerHTML = `
                <h2>📊 Detail Peluang: ${symbol}</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                    <div>
                        <h3>💰 Informasi Harga</h3>
                        <p><strong>Binance:</strong> $${opportunity.binance_price.toFixed(6)}</p>
                        <p><strong>Bybit:</strong> $${opportunity.bybit_price.toFixed(6)}</p>
                        <p><strong>Profit Kotor:</strong> <span class="profit-positive">${opportunity.profit_percentage.toFixed(3)}%</span></p>
                        <p><strong>Profit Bersih:</strong> <span class="profit-positive">${opportunity.net_profit.toFixed(3)}%</span></p>
                    </div>
                    <div>
                        <h3>📊 Volume & Likuiditas</h3>
                        <p><strong>Volume 24h:</strong> $${formatNumber(opportunity.volume_24h)}</p>
                        <p><strong>Volume Binance:</strong> $${formatNumber(opportunity.binance_volume)}</p>
                        <p><strong>Volume Bybit:</strong> $${formatNumber(opportunity.bybit_volume)}</p>
                        <p><strong>Arah Trading:</strong> ${opportunity.direction === 'binance_to_bybit' ? 'Binance → Bybit' : 'Bybit → Binance'}</p>
                    </div>
                </div>

                <h3>🌐 Jaringan Transfer Tersedia</h3>
                <div style="display: flex; gap: 10px; margin: 10px 0;">
                    ${opportunity.networks.map(network => `<span class="direction-badge direction-binance">${network}</span>`).join('')}
                </div>

                <h3>🧮 Kalkulator Profit</h3>
                <div style="margin: 15px 0;">
                    <label>Jumlah Investasi ($):</label>
                    <input type="number" id="investment-amount" value="1000" min="10" max="10000" style="width: 100%; padding: 10px; margin: 5px 0; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 5px; color: white;">
                    <div id="profit-calculation" style="margin-top: 10px; padding: 15px; background: rgba(0,255,136,0.1); border-radius: 10px;">
                        <p><strong>Profit Estimasi:</strong> $<span id="estimated-profit">0</span></p>
                        <p><strong>ROI:</strong> <span id="estimated-roi">0</span>%</p>
                    </div>
                </div>

                <div style="display: flex; gap: 15px; margin-top: 20px;">
                    <a href="${opportunity.binance_link}" target="_blank" class="btn" style="flex: 1;">🔗 Trade di Binance</a>
                    <a href="${opportunity.bybit_link}" target="_blank" class="btn" style="flex: 1;">🔗 Trade di Bybit</a>
                </div>
            `;

            modal.style.display = 'block';

            // Setup profit calculator
            const investmentInput = document.getElementById('investment-amount');
            investmentInput.addEventListener('input', function() {
                const amount = parseFloat(this.value) || 0;
                const profit = amount * (opportunity.net_profit / 100);
                document.getElementById('estimated-profit').textContent = profit.toFixed(2);
                document.getElementById('estimated-roi').textContent = opportunity.net_profit.toFixed(2);
            });

            // Trigger initial calculation
            investmentInput.dispatchEvent(new Event('input'));
        }

        function closeModal() {
            document.getElementById('detail-modal').style.display = 'none';
        }

        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = 'toast show';

            if (type === 'error') {
                toast.style.background = 'rgba(255, 71, 87, 0.9)';
            } else {
                toast.style.background = 'rgba(0, 255, 136, 0.9)';
            }

            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        function toggleAutoScan() {
            autoScanEnabled = !autoScanEnabled;
            const toggle = document.getElementById('auto-scan-toggle');

            if (autoScanEnabled) {
                toggle.classList.add('active');
                startAutoScan();
                showToast('✅ Auto scan diaktifkan');
            } else {
                toggle.classList.remove('active');
                stopAutoScan();
                showToast('⏸️ Auto scan dinonaktifkan');
            }
        }

        function startAutoScan() {
            if (autoScanEnabled) {
                scanInterval = setInterval(loadOpportunities, 30000); // 30 detik
            }
        }

        function stopAutoScan() {
            if (scanInterval) {
                clearInterval(scanInterval);
            }
        }

        async function triggerManualScan() {
            showToast('🔄 Memulai scan manual...');

            try {
                const response = await fetch('/api/scan');
                const data = await response.json();

                if (data.success) {
                    showToast(`✅ Scan selesai: ${data.opportunities_found} peluang ditemukan`);
                    loadOpportunities();
                } else {
                    showToast('❌ Scan gagal: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('❌ Error saat scan: ' + error.message, 'error');
            }
        }

        function applyFilters() {
            currentFilters.min_profit = parseFloat(document.getElementById('min-profit').value);
            currentFilters.max_profit = parseFloat(document.getElementById('max-profit').value);
            currentFilters.min_volume = parseFloat(document.getElementById('min-volume').value);
            currentFilters.network = document.getElementById('network-filter').value;

            loadOpportunities();
            showToast('🎛️ Filter diterapkan');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('detail-modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            } else if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
                event.preventDefault();
                triggerManualScan();
            }
        });
    </script>
</body>
</html>
"""

# Background Scanner Thread
class BackgroundScanner:
    """Background scanner untuk auto-update peluang"""
    def __init__(self, engine: ArbitrageEngine):
        self.engine = engine
        self.running = False
        self.thread = None

    def start(self):
        """Start background scanning"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._scan_loop, daemon=True)
            self.thread.start()
            logger.info("Background scanner started")

    def stop(self):
        """Stop background scanning"""
        self.running = False
        if self.thread:
            self.thread.join()
        logger.info("Background scanner stopped")

    def _scan_loop(self):
        """Main scanning loop"""
        while self.running:
            try:
                # Scan opportunities
                opportunities = self.engine.scan_opportunities()

                # Log high profit opportunities
                high_profit_ops = [op for op in opportunities if op.profit_percentage > 5]
                if high_profit_ops:
                    logger.info(f"[ALERT] {len(high_profit_ops)} high profit opportunities found!")
                    for op in high_profit_ops[:3]:  # Log top 3
                        logger.info(f"   {op.symbol}: {op.profit_percentage:.2f}% profit")

                # Wait 30 seconds before next scan
                for _ in range(30):
                    if not self.running:
                        break
                    time.sleep(1)

            except Exception as e:
                logger.error(f"Background scan error: {e}")
                time.sleep(10)  # Wait 10 seconds on error

def setup_logging():
    """Setup enhanced logging"""
    # Create logs directory if not exists
    import os
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # Configure logging with rotation
    from logging.handlers import RotatingFileHandler

    # File handler with rotation
    file_handler = RotatingFileHandler(
        'logs/arbitrage.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.INFO)

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.handlers.clear()
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

def print_startup_banner():
    """Print startup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                     🚀 CryptoArb Pro v1.0                    ║
    ║              Deteksi Arbitrase Cryptocurrency                ║
    ║                    Binance vs Bybit                          ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  📊 Target: 300+ trading pairs                              ║
    ║  ⚡ Performance: 20+ tokens/second, <2GB memory             ║
    ║  🔄 Update: Real-time setiap 30 detik                       ║
    ║  💰 Profit Range: 0.5% - 200%                               ║
    ║  🌐 UI: Dark Futuristic Theme dengan Glassmorphism          ║
    ║  🔗 API: Binance + Bybit (Public endpoints only)            ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  Author: BOBACHEESE                                          ║
    ║  Framework: Flask + HTML/CSS/JavaScript                     ║
    ║  Environment: Conda on Windows 11                           ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def validate_environment():
    """Validate environment and dependencies"""
    try:
        # Test imports
        requests.get  # Test requests module
        Flask  # Test flask module
        logger.info("[OK] All dependencies available")
        return True
    except (ImportError, AttributeError) as e:
        logger.error(f"[ERROR] Missing dependency: {e}")
        return False

def test_api_connections():
    """Test API connections"""
    logger.info("[INFO] Testing API connections...")

    # Test Binance
    try:
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=5)
        if response.status_code == 200:
            logger.info("[OK] Binance API connection successful")
        else:
            logger.warning(f"[WARN] Binance API returned status {response.status_code}")
    except Exception as e:
        logger.error(f"[ERROR] Binance API connection failed: {e}")

    # Test Bybit
    try:
        response = requests.get("https://api.bybit.com/v5/market/time", timeout=5)
        if response.status_code == 200:
            logger.info("[OK] Bybit API connection successful")
        else:
            logger.warning(f"[WARN] Bybit API returned status {response.status_code}")
    except Exception as e:
        logger.error(f"[ERROR] Bybit API connection failed: {e}")

if __name__ == '__main__':
    # Setup
    setup_logging()
    print_startup_banner()

    # Validate environment
    if not validate_environment():
        logger.error("[ERROR] Environment validation failed. Please install required dependencies.")
        exit(1)

    # Test API connections
    test_api_connections()

    # Initialize background scanner
    background_scanner = BackgroundScanner(arbitrage_engine)

    try:
        logger.info("[START] Starting CryptoArb Pro...")

        # Start background scanner
        background_scanner.start()

        # Initial scan
        logger.info("[SCAN] Performing initial scan...")
        initial_opportunities = arbitrage_engine.scan_opportunities()
        logger.info(f"[RESULT] Initial scan found {len(initial_opportunities)} opportunities")

        # Start Flask app
        logger.info("[WEB] Starting web interface...")
        logger.info("[INFO] Access the application at: http://localhost:5000")
        logger.info("[INFO] Use Ctrl+C to stop the application")

        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False  # Disable reloader to prevent duplicate background threads
        )

    except KeyboardInterrupt:
        logger.info("[STOP] Shutdown signal received...")
    except Exception as e:
        logger.error(f"[ERROR] Application error: {e}")
    finally:
        # Cleanup
        logger.info("[CLEANUP] Cleaning up...")
        background_scanner.stop()
        logger.info("[OK] CryptoArb Pro stopped successfully")
        print("\n[THANKS] Terima kasih telah menggunakan CryptoArb Pro!")
        print("[SUPPORT] Untuk support: https://github.com/bobacheese")
