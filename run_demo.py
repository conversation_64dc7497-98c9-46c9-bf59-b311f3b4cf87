#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo runner untuk CryptoArb Pro
Menjalankan program dengan data demo ketika API tidak tersedia
"""

import sys
import os
import logging
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import demo data
from demo_data import (
    generate_demo_binance_data, 
    generate_demo_bybit_data, 
    create_demo_opportunities,
    get_demo_status,
    DEMO_MODE
)

def setup_demo_logging():
    """Setup logging untuk demo mode"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - [DEMO] - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('demo_arbitrage.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def print_demo_banner():
    """Print demo banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                🎭 CryptoArb Pro - DEMO MODE 🎭               ║
    ║              Deteksi Arbitrase Cryptocurrency                ║
    ║                    Binance vs Bybit                          ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  📊 Demo Data: 25 trading pairs                             ║
    ║  ⚡ Simulated Performance: 20+ tokens/second                 ║
    ║  🔄 Update: Real-time setiap 30 detik                       ║
    ║  💰 Demo Opportunities: 5 peluang arbitrase                 ║
    ║  🌐 UI: Dark Futuristic Theme dengan Glassmorphism          ║
    ║  🎯 Mode: DEMO - Data simulasi untuk testing                ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  Author: BOBACHEESE                                          ║
    ║  Framework: Flask + HTML/CSS/JavaScript                     ║
    ║  Environment: Demo Mode - No Real API Calls                 ║
    ╚══════════════════════════════════════════════════════════════╝
    
    🎭 DEMO MODE AKTIF - Data yang ditampilkan adalah simulasi
    📚 Gunakan untuk testing dan pembelajaran interface
    ⚠️  Jangan gunakan untuk trading sesungguhnya
    """
    print(banner)

def create_demo_main():
    """Create demo version of main.py with simulated data"""
    
    # Import the original modules
    from main import (
        Flask, render_template_string, jsonify, request,
        ArbitrageOpportunity, ArbitrageEngine, BackgroundScanner,
        HTML_TEMPLATE, app
    )
    
    logger = logging.getLogger(__name__)
    
    # Override ArbitrageEngine methods for demo
    class DemoArbitrageEngine(ArbitrageEngine):
        def __init__(self):
            super().__init__()
            self.demo_opportunities = []
            self.setup_demo_data()
        
        def setup_demo_data(self):
            """Setup demo opportunities"""
            demo_ops = create_demo_opportunities()
            self.demo_opportunities = []
            
            for op_data in demo_ops:
                opportunity = ArbitrageOpportunity(
                    symbol=op_data['symbol'],
                    binance_price=op_data['binance_price'],
                    bybit_price=op_data['bybit_price'],
                    profit_percentage=op_data['profit_percentage'],
                    volume_24h=op_data['volume_24h'],
                    binance_volume=op_data['volume_24h'] * 0.6,
                    bybit_volume=op_data['volume_24h'] * 0.4,
                    direction=op_data['direction'],
                    timestamp=datetime.now(),
                    networks=op_data['networks']
                )
                self.demo_opportunities.append(opportunity)
        
        def scan_opportunities(self):
            """Return demo opportunities"""
            logger.info("[DEMO] Scanning demo opportunities...")
            
            # Simulate some variation in opportunities
            import random
            available_ops = random.sample(self.demo_opportunities, 
                                        random.randint(3, len(self.demo_opportunities)))
            
            # Update timestamps
            for op in available_ops:
                op.timestamp = datetime.now()
                # Add small random variation to prices
                variation = random.uniform(-0.001, 0.001)
                op.binance_price *= (1 + variation)
                op.bybit_price *= (1 - variation)
                
                # Recalculate profit
                if op.binance_price > op.bybit_price:
                    op.profit_percentage = ((op.binance_price - op.bybit_price) / op.bybit_price) * 100
                    op.direction = 'bybit_to_binance'
                else:
                    op.profit_percentage = ((op.bybit_price - op.binance_price) / op.binance_price) * 100
                    op.direction = 'binance_to_bybit'
            
            self.opportunities = available_ops
            self.last_update = datetime.now()
            self.stats = {
                'total_scanned': 25,
                'opportunities_found': len(available_ops),
                'average_profit': sum(op.profit_percentage for op in available_ops) / len(available_ops) if available_ops else 0,
                'scan_duration': random.uniform(1.0, 2.5)
            }
            
            logger.info(f"[DEMO] Found {len(available_ops)} demo opportunities")
            return available_ops
        
        def get_status(self):
            """Return demo status"""
            return get_demo_status()
    
    # Replace global engine with demo version
    demo_engine = DemoArbitrageEngine()
    
    # Override Flask routes
    @app.route('/api/opportunities')
    def get_demo_opportunities():
        """Demo API endpoint untuk peluang arbitrase"""
        try:
            # Filter berdasarkan parameter
            min_profit = float(request.args.get('min_profit', 0.5))
            max_profit = float(request.args.get('max_profit', 200))
            min_volume = float(request.args.get('min_volume', 10000))
            
            filtered_opportunities = []
            for op in demo_engine.opportunities:
                if (min_profit <= op.profit_percentage <= max_profit and 
                    op.volume_24h >= min_volume):
                    filtered_opportunities.append({
                        'symbol': op.symbol,
                        'binance_price': op.binance_price,
                        'bybit_price': op.bybit_price,
                        'profit_percentage': round(op.profit_percentage, 3),
                        'net_profit': round(op.net_profit, 3),
                        'volume_24h': op.volume_24h,
                        'direction': op.direction,
                        'timestamp': op.timestamp.isoformat(),
                        'networks': op.networks,
                        'binance_link': f"https://www.binance.com/en/trade/{op.symbol}",
                        'bybit_link': f"https://www.bybit.com/en/trade/spot/{op.symbol}"
                    })
            
            return jsonify({
                'success': True,
                'data': filtered_opportunities,
                'count': len(filtered_opportunities),
                'demo_mode': True
            })
            
        except Exception as e:
            logger.error(f"[DEMO] API error: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/status')
    def get_demo_status_api():
        """Demo API endpoint untuk status sistem"""
        status = demo_engine.get_status()
        status['demo_mode'] = True
        return jsonify(status)

    @app.route('/api/scan')
    def trigger_demo_scan():
        """Demo trigger manual scan"""
        try:
            opportunities = demo_engine.scan_opportunities()
            return jsonify({
                'success': True,
                'opportunities_found': len(opportunities),
                'scan_time': demo_engine.stats['scan_duration'],
                'demo_mode': True
            })
        except Exception as e:
            logger.error(f"[DEMO] Manual scan error: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    return demo_engine

if __name__ == '__main__':
    # Setup demo environment
    setup_demo_logging()
    print_demo_banner()
    
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("[DEMO] Starting CryptoArb Pro in Demo Mode...")
        
        # Create demo engine
        demo_engine = create_demo_main()
        
        # Initial demo scan
        logger.info("[DEMO] Performing initial demo scan...")
        initial_opportunities = demo_engine.scan_opportunities()
        logger.info(f"[DEMO] Initial scan found {len(initial_opportunities)} demo opportunities")
        
        # Start Flask app
        logger.info("[DEMO] Starting demo web interface...")
        logger.info("[DEMO] Access the application at: http://localhost:5000")
        logger.info("[DEMO] Use Ctrl+C to stop the demo application")
        
        from main import app
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        logger.info("[DEMO] Demo shutdown signal received...")
    except Exception as e:
        logger.error(f"[DEMO] Demo application error: {e}")
    finally:
        logger.info("[DEMO] Demo cleanup completed")
        print("\n[DEMO] Terima kasih telah mencoba CryptoArb Pro Demo!")
        print("[INFO] Untuk versi lengkap dengan API real: python main.py")
